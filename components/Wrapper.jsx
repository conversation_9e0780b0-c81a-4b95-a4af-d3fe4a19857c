import React from 'react'

// Main content wrapper with consistent padding
const Wrapper = ({ children, className = "" }) => {
  return (
    <div className={`px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16 2xl:px-20 ${className}`}>
        {children}
    </div>
  )
}

// Container wrapper with max-width for centered content
export const Container = ({ children, className = "" }) => {
  return (
    <div className={`max-w-7xl mx-auto px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16 2xl:px-20 ${className}`}>
      {children}
    </div>
  )
}

// Section wrapper for full-width backgrounds with contained content
export const Section = ({ children, className = "", containerClassName = "" }) => {
  return (
    <section className={className}>
      <Container className={containerClassName}>
        {children}
      </Container>
    </section>
  )
}

export default Wrapper