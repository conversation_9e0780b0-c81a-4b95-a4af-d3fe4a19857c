import React from "react";
import HeroInfo from "./HeroInfo";
import FilterBar from "./FilterBar";
import BannerImage from "./BannerImage";
import FeatureCards from "./FeatureCards";
import { Container } from "@/components/Wrapper";

const HeroSection = () => {
  return (
    <section className="relative min-h-[80vh] flex flex-col">
      {/* Hero Content */}
      <Container>
        <div className="flex flex-col items-center w-full pt-8 pb-16">
          <HeroInfo />
          <FilterBar />
        </div>
      </Container>

      {/* Banner Image - Full Width */}
      <div className="flex-1 relative">
        <BannerImage />
      </div>

      {/* Feature Cards */}
      <Container>
        <FeatureCards />
      </Container>
    </section>
  );
};

export default HeroSection;
