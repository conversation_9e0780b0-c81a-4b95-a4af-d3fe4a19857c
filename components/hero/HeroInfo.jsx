import React from "react";
import data from "@/data/hero.json";

const HeroInfo = () => {
  return (
    <div className="w-full flex flex-col items-center justify-center text-center py-8 md:py-12">
      {/* Welcome Badge */}
      <div className="mb-6 flex items-center justify-center gap-2">
        <span className="h-2 w-2 rounded-full bg-[#04CE78] inline-block" />
        <span className="text-[#04CE78] font-semibold tracking-wider text-sm md:text-base uppercase">
          {data.welcome}
        </span>
        <span className="h-2 w-2 rounded-full bg-[#04CE78] inline-block" />
      </div>

      {/* Main Headline */}
      <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6 max-w-5xl leading-tight">
        {data.title}
      </h1>

      {/* Subtitle */}
      <p className="text-lg md:text-xl text-white/90 max-w-2xl mx-auto mb-8">
        {data.subtitle}
      </p>
    </div>
  );
};

export default HeroInfo;