"use client";

import React, { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import {
  NavigationMenu,
  NavigationMenuList,
  NavigationMenuItem,
  NavigationMenuTrigger,
  NavigationMenuContent,
  NavigationMenuLink,
  NavigationMenuViewport,
} from "@/components/ui/navigation-menu";
import { Button } from "@/components/ui/button";
import {
  ArrowRightIcon,
  UserCircle,
  Menu as MenuIcon,
  X as CloseIcon,
} from "lucide-react";
import navLinks from "@/data/navlinks.json";
const Navbar = () => {
  const [mobileOpen, setMobileOpen] = useState(false);

  return (
      <div className="flex flex-col md:flex-row justify-between py-[31px] rounded-t-[20px] items-center w-full bg-transparent relative">
        {/* Logo and mobile menu button */}
        <div className="flex justify-between w-full md:w-auto">
          <Link href="/">
            <Image
              src="/Logo.png"
              alt="Aarogya Global Logo"
              width={149}
              height={39}
              className="h-auto"
            />
          </Link>

          <button
            className="md:hidden text-white z-20 ml-2"
            onClick={() => setMobileOpen((v) => !v)}
            aria-label="Toggle menu"
          >
            {mobileOpen ? (
              <CloseIcon className="w-7 h-7" />
            ) : (
              <MenuIcon className="w-7 h-7" />
            )}
          </button>
        </div>

        {/* Navigation menu */}
        <nav
          className={`transition-all duration-300 md:flex md:items-center
            fixed md:relative top-0 left-0 w-full h-screen md:h-auto bg-[#1a0856] md:bg-transparent z-10 md:z-auto
            ${mobileOpen ? "flex flex-col items-center pt-24 gap-8" : "hidden"}`}
        >
          <NavigationMenu className="bg-transparent shadow-none">
            <NavigationMenuList className="flex flex-col md:flex-row gap-4 md:gap-0">
              {navLinks.map((link) =>
                link.dropdown ? (
                  <NavigationMenuItem key={link.label} className="relative">
                    <NavigationMenuTrigger
                      className={`text-white bg-transparent px-3 py-2 font-semibold flex items-center gap-1 focus:bg-transparent focus:text-[#04CE78] data-[state=open]:text-[#04CE78]`}
                    >
                      {link.label}
                    </NavigationMenuTrigger>
                    <NavigationMenuContent className="absolute left-0 md:left-auto bg-white min-w-[180px] rounded-md shadow-lg mt-2">
                      <ul className="py-2">
                        {link.dropdown.map((item) => (
                          <li key={item.label}>
                            <NavigationMenuLink asChild>
                              <Link
                                href={item.href}
                                className="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded transition-colors"
                                onClick={() => setMobileOpen(false)}
                              >
                                {item.label}
                              </Link>
                            </NavigationMenuLink>
                          </li>
                        ))}
                      </ul>
                    </NavigationMenuContent>
                  </NavigationMenuItem>
                ) : (
                  <NavigationMenuItem key={link.label}>
                    <NavigationMenuLink asChild>
                      <Link
                        href={link.href}
                        className={`px-3 py-2 font-semibold transition-colors rounded text-white focus:text-[#04CE78] data-[active=true]:text-[#04CE78] ${
                          link.highlight ? "text-[#04CE78]" : ""
                        }`}
                        onClick={() => setMobileOpen(false)}
                      >
                        {link.label}
                      </Link>
                    </NavigationMenuLink>
                  </NavigationMenuItem>
                )
              )}
            </NavigationMenuList>
            <NavigationMenuViewport />
          </NavigationMenu>
        </nav>

        {/* Right Side (Login/Register & Appointment) */}
        <div className={`hidden md:flex items-center gap-4 min-w-max`}>
          <Link
            href="/login"
            className="flex items-center gap-2 text-white hover:text-cyan-300 font-semibold transition-colors"
          >
            <UserCircle className="w-5 h-5" />
            Login/Register
          </Link>
          <Button
            asChild
            className="bg-green-500 hover:bg-green-600 text-white font-bold p-7  rounded-lg shadow flex items-center gap-2 text-base"
          >
            <Link href="/appointment">
              Make An Appointment <ArrowRightIcon className="w-5 h-5" />
            </Link>
          </Button>
        </div>

        {/* Mobile menu buttons */}
        {mobileOpen && (
          <div className="flex flex-col items-center gap-4 mt-8 md:hidden">
            <Link
              href="/login"
              className="flex items-center gap-2 text-white hover:text-cyan-300 font-semibold transition-colors"
              onClick={() => setMobileOpen(false)}
            >
              <UserCircle className="w-5 h-5" />
              Login/Register
            </Link>
            <Button
              asChild
              className="bg-green-500 hover:bg-green-600 text-white font-bold px-6 py-2 rounded-lg shadow flex items-center gap-2 text-base"
            >
              <Link href="/appointment" onClick={() => setMobileOpen(false)}>
                Make An Appointment <ArrowRightIcon className="w-5 h-5" />
              </Link>
            </Button>
          </div>
        )}

        {/* Overlay for mobile menu */}
        {mobileOpen && (
          <div
            className="fixed inset-0 bg-black/40 z-0 md:hidden"
            onClick={() => setMobileOpen(false)}
          />
        )}
      </div>
  );
};

export default Navbar;