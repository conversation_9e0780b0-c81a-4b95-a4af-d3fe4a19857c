import React from "react";
import { Section } from "@/components/Wrapper";

const stats = [
  { label: "Years of Experience", value: "30+", color: "text-blue-600" },
  { label: "Expert Doctors", value: "240+", color: "text-green-600" },
  { label: "Countries Served", value: "51+", color: "text-purple-600" },
  { label: "Happy Patients", value: "100+", color: "text-orange-600" },
];

const Stats = () => (
  <Section className="py-16 md:py-20 bg-white">
    <div className="grid grid-cols-2 md:grid-cols-4 gap-8 md:gap-12">
      {stats.map((stat, idx) => (
        <div key={idx} className="flex flex-col items-center text-center">
          <span className={`text-4xl md:text-5xl lg:text-6xl font-bold ${stat.color} mb-2`}>
            {stat.value}
          </span>
          <span className="text-gray-700 text-sm md:text-base font-medium">
            {stat.label}
          </span>
        </div>
      ))}
    </div>
  </Section>
);

export default Stats;
