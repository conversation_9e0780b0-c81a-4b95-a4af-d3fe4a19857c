import React from "react";
import { Section } from "@/components/Wrapper";
import { Heart, Bone, TestTube, Microscope, Brain, Eye, Stethoscope, Pill } from "lucide-react";

const specialties = [
  {
    icon: <Heart className="w-8 h-8 text-red-500" />,
    label: "Cardiology",
    description: "Heart & Cardiovascular",
    bgColor: "bg-red-50",
    borderColor: "border-red-200"
  },
  {
    icon: <Bone className="w-8 h-8 text-blue-500" />,
    label: "Orthopedics",
    description: "Bones & Joints",
    bgColor: "bg-blue-50",
    borderColor: "border-blue-200"
  },
  {
    icon: <Brain className="w-8 h-8 text-purple-500" />,
    label: "Neurology",
    description: "Brain & Nervous System",
    bgColor: "bg-purple-50",
    borderColor: "border-purple-200"
  },
  {
    icon: <Eye className="w-8 h-8 text-green-500" />,
    label: "Ophthalmology",
    description: "Eye Care",
    bgColor: "bg-green-50",
    borderColor: "border-green-200"
  },
  {
    icon: <TestTube className="w-8 h-8 text-orange-500" />,
    label: "Laboratory",
    description: "Diagnostic Tests",
    bgColor: "bg-orange-50",
    borderColor: "border-orange-200"
  },
];

const TopSpecialties = () => (
  <Section className="py-16 md:py-20 bg-gray-50">
    <div className="text-center mb-12">
      <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
        Our Medical Specialties
      </h2>
      <p className="text-lg text-gray-600 max-w-2xl mx-auto">
        Comprehensive healthcare services across multiple specialties with world-class medical professionals
      </p>
    </div>

    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
      {specialties.map((spec, idx) => (
        <div
          key={idx}
          className={`flex flex-col items-center ${spec.bgColor} ${spec.borderColor} border rounded-2xl p-6 hover:shadow-lg transition-all duration-300 cursor-pointer group`}
        >
          <div className="mb-4 group-hover:scale-110 transition-transform duration-300">
            {spec.icon}
          </div>
          <h3 className="font-bold text-lg mb-2 text-gray-900 text-center">
            {spec.label}
          </h3>
          <p className="text-gray-600 text-sm text-center">
            {spec.description}
          </p>
        </div>
      ))}
    </div>
  </Section>
);

export default TopSpecialties;
