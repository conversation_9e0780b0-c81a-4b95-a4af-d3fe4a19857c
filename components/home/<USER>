import React from "react";
import { Section } from "@/components/Wrapper";
import { Star, MapPin, Calendar } from "lucide-react";
import { Button } from "@/components/ui/button";

const doctors = [
  {
    name: "Dr. <PERSON>",
    specialty: "Cardiology",
    img: "/doctor1.png",
    rating: 4.9,
    reviews: 127,
    location: "New York, USA",
    experience: "15+ years",
    price: "$150"
  },
  {
    name: "Dr. <PERSON>",
    specialty: "Orthopedics",
    img: "/doctor2.png",
    rating: 4.8,
    reviews: 98,
    location: "Los Angeles, USA",
    experience: "12+ years",
    price: "$180"
  },
  {
    name: "Dr. <PERSON>",
    specialty: "Neurology",
    img: "/doctor3.png",
    rating: 4.9,
    reviews: 156,
    location: "Chicago, USA",
    experience: "18+ years",
    price: "$200"
  },
  {
    name: "Dr. <PERSON>",
    specialty: "General Medicine",
    img: "/doctor4.png",
    rating: 4.7,
    reviews: 89,
    location: "Miami, USA",
    experience: "10+ years",
    price: "$120"
  },
];

const TopDoctors = () => (
  <Section className="py-16 md:py-20 bg-white">
    <div className="text-center mb-12">
      <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
        Meet Our Top Rated Doctors
      </h2>
      <p className="text-lg text-gray-600 max-w-2xl mx-auto">
        Connect with experienced healthcare professionals who are dedicated to providing exceptional care
      </p>
    </div>

    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {doctors.map((doc, idx) => (
        <div key={idx} className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
          {/* Doctor Image */}
          <div className="relative mb-4">
            <img
              src={doc.img}
              alt={doc.name}
              className="w-20 h-20 rounded-full object-cover mx-auto border-4 border-white shadow-md"
            />
          </div>

          {/* Doctor Info */}
          <div className="text-center mb-4">
            <h3 className="font-bold text-lg text-gray-900 mb-1">{doc.name}</h3>
            <p className="text-blue-600 font-medium text-sm mb-2">{doc.specialty}</p>

            {/* Rating */}
            <div className="flex items-center justify-center gap-1 mb-2">
              <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
              <span className="font-semibold text-gray-900">{doc.rating}</span>
              <span className="text-gray-500 text-sm">({doc.reviews} reviews)</span>
            </div>

            {/* Location & Experience */}
            <div className="space-y-1 text-sm text-gray-600">
              <div className="flex items-center justify-center gap-1">
                <MapPin className="w-3 h-3" />
                <span>{doc.location}</span>
              </div>
              <div className="flex items-center justify-center gap-1">
                <Calendar className="w-3 h-3" />
                <span>{doc.experience}</span>
              </div>
            </div>
          </div>

          {/* Price & Action */}
          <div className="text-center">
            <p className="text-lg font-bold text-green-600 mb-3">From {doc.price}</p>
            <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 rounded-lg">
              Book Appointment
            </Button>
          </div>
        </div>
      ))}
    </div>

    {/* View All Button */}
    <div className="text-center mt-12">
      <Button variant="outline" className="px-8 py-3 text-blue-600 border-blue-600 hover:bg-blue-50">
        View All Doctors
      </Button>
    </div>
  </Section>
);

export default TopDoctors;
