import React from "react";
import { Section } from "@/components/Wrapper";

// Placeholder logos - replace with actual partner logos
const partners = [
  { name: "Partner 1", logo: "/logo1.png" },
  { name: "Partner 2", logo: "/logo2.png" },
  { name: "Partner 3", logo: "/logo3.png" },
  { name: "Partner 4", logo: "/logo4.png" },
  { name: "Partner 5", logo: "/logo5.png" },
  { name: "Partner 6", logo: "/logo1.png" },
];

const TrustedBy = () => (
  <Section className="py-12 md:py-16 bg-gray-50">
    <div className="text-center mb-8">
      <p className="text-gray-600 font-medium text-sm md:text-base uppercase tracking-wider">
        TRUSTED BY LEADING HEALTHCARE INSTITUTIONS
      </p>
    </div>

    {/* Partner Logos Grid */}
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8 items-center justify-items-center">
      {partners.map((partner, idx) => (
        <div
          key={idx}
          className="flex items-center justify-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 w-full h-20"
        >
          <img
            src={partner.logo}
            alt={`${partner.name} logo`}
            className="h-8 md:h-10 object-contain opacity-70 hover:opacity-100 transition-opacity duration-200"
          />
        </div>
      ))}
    </div>
  </Section>
);

export default TrustedBy;
