import { Poppins } from "next/font/google";
import "./globals.css";
import Navbar from "@/components/navbar/Navbar";
import TopBar from "@/components/navbar/TopBar";
import BottomFooter from "@/components/footer/BottomFooter";
import Footer from "@/components/footer/Footer";
import { Container } from "@/components/Wrapper";

const poppins = Poppins({
  weight: ["400", "500", "600", "700"],
  variable: "--font-poppins",
  subsets: ["latin"],
});

export const metadata = {
  title: "Aarogya Global",
  description: "Aarogya Global world best health care",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body className={`${poppins.variable} antialiased`}>
        <div
          className="bg-contain"
          style={{ backgroundImage: "url(/backgroundImg.png)" }}
        >
          <TopBar />
          <Container>
            <Navbar />
          </Container>
          {children}
        </div>
        <Container>
          <Footer />
        </Container>
        <BottomFooter />
      </body>
    </html>
  );
}
